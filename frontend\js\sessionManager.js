/**
 * 会话管理器
 * 负责会话的创建、管理和状态维护
 */
class SessionManager {
    constructor() {
        this.currentSession = null;
        this.dbHelper = window.indexedDBHelper;
    }

    /**
     * 初始化会话管理器
     */
    async init() {
        if (!this.dbHelper) {
            throw new Error('IndexedDB Helper 未初始化');
        }
        await this.dbHelper.init();
        console.log('会话管理器初始化完成');
    }

    /**
     * 生成会话ID
     */
    generateSessionId() {
        // 使用nanoid风格的ID生成
        const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
        let result = '';
        for (let i = 0; i < 21; i++) {
            result += chars.charAt(Math.floor(Math.random() * chars.length));
        }
        return result;
    }

    /**
     * 生成会话标题
     */
    generateSessionTitle(sessionType, dataScope, courseName, courseMaterialName) {
        if (sessionType === 'course_chat') {
            if (dataScope === 'course') {
                return `课程：${courseName}`;
            } else if (dataScope === 'course_material') {
                return `课件：${courseMaterialName}`;
            }
        } else if (sessionType === 'free_chat') {
            // 未来扩展功能，当前不实现
            return '自由聊天';
        }
        return '未知会话';
    }

    /**
     * 检查标题唯一性并处理重复
     */
    async ensureUniqueTitle(title) {
        const existingSessions = await this.dbHelper.listSessions();
        const existingTitles = existingSessions.map(s => s.title);
        
        if (!existingTitles.includes(title)) {
            return title;
        }

        // 如果标题重复，添加时间戳
        const timestamp = new Date().toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit'
        });
        return `${title} (${timestamp})`;
    }

    /**
     * 创建新会话
     */
    async createSession(sessionConfig) {
        const {
            sessionType = 'course_chat',
            dataScope,
            mode = 'condense_question',
            courseId,
            courseName,
            courseMaterialId,
            courseMaterialName
        } = sessionConfig;

        // 验证必需参数
        if (!dataScope) {
            throw new Error('data_scope 参数是必需的');
        }

        if (dataScope === 'course' && (!courseId || !courseName)) {
            throw new Error('当 data_scope 为 course 时，course_id 和 course_name 是必需的');
        }

        if (dataScope === 'course_material' && (!courseMaterialId || !courseMaterialName)) {
            throw new Error('当 data_scope 为 course_material 时，course_material_id 和 course_material_name 是必需的');
        }

        // 生成会话ID
        const sessionId = this.generateSessionId();

        // 生成会话标题
        const baseTitle = this.generateSessionTitle(sessionType, dataScope, courseName, courseMaterialName);
        const title = await this.ensureUniqueTitle(baseTitle);

        // 构建会话数据
        const sessionData = {
            session_id: sessionId,
            session_type: sessionType,
            data_scope: dataScope,
            title: title,
            frontend_session_chat_history: [],
            chatSummaryBuffer: null,
            mode: mode
        };

        // 根据data_scope添加相应字段
        if (dataScope === 'course') {
            sessionData.course_id = courseId;
            sessionData.course_name = courseName;
        } else if (dataScope === 'course_material') {
            sessionData.course_material_id = courseMaterialId;
            sessionData.course_material_name = courseMaterialName;
        }

        // 保存到数据库
        const createdSession = await this.dbHelper.createSession(sessionData);
        
        // 设置为当前会话
        this.currentSession = createdSession;
        
        console.log('新会话创建成功:', createdSession);
        return createdSession;
    }

    /**
     * 查找或创建会话
     * 基于data_scope和对应ID的唯一性约束
     */
    async findOrCreateSession(sessionConfig) {
        const { dataScope, courseId, courseMaterialId } = sessionConfig;

        // 确定查找的ID
        const scopeId = dataScope === 'course' ? courseId : courseMaterialId;

        // 查找现有会话
        const existingSession = await this.dbHelper.findSessionByDataScope(dataScope, scopeId);

        if (existingSession) {
            console.log('找到现有会话:', existingSession);
            this.currentSession = existingSession;
            return existingSession;
        }

        // 创建新会话
        console.log('未找到现有会话，创建新会话');
        return this.createSession(sessionConfig);
    }

    /**
     * 加载指定会话
     */
    async loadSession(sessionId) {
        const session = await this.dbHelper.getSession(sessionId);
        if (session) {
            this.currentSession = session;
            console.log('会话加载成功:', session);
        } else {
            throw new Error(`会话 ${sessionId} 不存在`);
        }
        return session;
    }

    /**
     * 获取当前会话
     */
    getCurrentSession() {
        return this.currentSession;
    }

    /**
     * 添加聊天消息到当前会话
     */
    async addChatMessage(role, content) {
        if (!this.currentSession) {
            throw new Error('没有活跃的会话');
        }

        const message = {
            role: role,
            content: content,
            timestamp: Date.now()
        };

        // 添加到前端聊天历史
        this.currentSession.frontend_session_chat_history.push(message);

        // 更新数据库
        await this.dbHelper.updateSession(this.currentSession.session_id, {
            frontend_session_chat_history: this.currentSession.frontend_session_chat_history
        });

        console.log('聊天消息已添加:', message);
        return message;
    }

    /**
     * 更新当前会话的ChatSummaryBuffer
     */
    async updateChatSummaryBuffer(chatSummaryBuffer) {
        if (!this.currentSession) {
            throw new Error('没有活跃的会话');
        }

        this.currentSession.chatSummaryBuffer = chatSummaryBuffer;

        // 更新数据库
        await this.dbHelper.updateSession(this.currentSession.session_id, {
            chatSummaryBuffer: chatSummaryBuffer
        });

        console.log('ChatSummaryBuffer已更新');
    }

    /**
     * 清空当前会话的聊天记录
     */
    async clearCurrentSessionHistory() {
        if (!this.currentSession) {
            throw new Error('没有活跃的会话');
        }

        // 清空前端聊天历史和ChatSummaryBuffer
        this.currentSession.frontend_session_chat_history = [];
        this.currentSession.chatSummaryBuffer = null;

        // 更新数据库
        await this.dbHelper.clearSessionHistory(this.currentSession.session_id);

        console.log('当前会话聊天记录已清空');
    }

    /**
     * 获取会话列表
     */
    async getSessionList(options = {}) {
        return this.dbHelper.listSessions(options);
    }

    /**
     * 删除会话
     */
    async deleteSession(sessionId) {
        await this.dbHelper.deleteSession(sessionId);
        
        // 如果删除的是当前会话，清空当前会话
        if (this.currentSession && this.currentSession.session_id === sessionId) {
            this.currentSession = null;
        }

        console.log('会话已删除:', sessionId);
    }

    /**
     * 清理过期会话
     */
    async cleanupExpiredSessions(days = 30) {
        return this.dbHelper.cleanupExpiredSessions(days);
    }

    /**
     * 搜索会话
     */
    async searchSessions(titleKeyword) {
        return this.dbHelper.searchSessionsByTitle(titleKeyword);
    }

    /**
     * 获取会话的RAG查询参数
     */
    getSessionRAGParams() {
        if (!this.currentSession) {
            throw new Error('没有活跃的会话');
        }

        const params = {
            session_id: this.currentSession.session_id,
            data_scope: this.currentSession.data_scope,
            mode: this.currentSession.mode,
            chatSummaryBuffer: this.currentSession.chatSummaryBuffer
        };

        // 根据data_scope添加相应参数
        if (this.currentSession.data_scope === 'course') {
            params.course_id = this.currentSession.course_id;
            params.course_name = this.currentSession.course_name;
        } else if (this.currentSession.data_scope === 'course_material') {
            params.course_material_id = this.currentSession.course_material_id;
            params.course_material_name = this.currentSession.course_material_name;
        }

        return params;
    }
}

// 创建全局实例
window.sessionManager = new SessionManager();
