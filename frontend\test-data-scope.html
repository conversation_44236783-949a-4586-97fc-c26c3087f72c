<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Data Scope 功能测试</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <link href="css/style.css" rel="stylesheet">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <div class="col-md-3 bg-light p-3">
                <h5><i class="bi bi-gear"></i> 测试配置</h5>
                
                <!-- 会话配置 -->
                <div class="card mb-3">
                    <div class="card-header">
                        <h6 class="mb-0">会话配置</h6>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="data-scope" class="form-label">Data Scope</label>
                            <select class="form-select" id="data-scope" onchange="toggleScopeFields()">
                                <option value="course">Course</option>
                                <option value="course_material">Course Material</option>
                            </select>
                        </div>

                        <!-- Course 字段 -->
                        <div id="course-fields">
                            <div class="mb-3">
                                <label for="course-id" class="form-label">Course ID</label>
                                <input type="text" class="form-control" id="course-id" value="course_01">
                            </div>
                            <div class="mb-3">
                                <label for="course-name" class="form-label">Course Name</label>
                                <input type="text" class="form-control" id="course-name" value="Python编程基础">
                            </div>
                        </div>

                        <!-- Course Material 字段 -->
                        <div id="course-material-fields" style="display: none;">
                            <div class="mb-3">
                                <label for="course-material-id" class="form-label">Course Material ID</label>
                                <input type="text" class="form-control" id="course-material-id" value="material_001">
                            </div>
                            <div class="mb-3">
                                <label for="course-material-name" class="form-label">Course Material Name</label>
                                <input type="text" class="form-control" id="course-material-name" value="Python第八章函数">
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="chat-mode" class="form-label">聊天模式</label>
                            <select class="form-select" id="chat-mode">
                                <option value="query">condense_question (检索模式)</option>
                                <option value="chat">SimpleChatEngine (直接聊天)</option>
                            </select>
                        </div>

                        <button class="btn btn-primary w-100" onclick="startChat()">
                            <i class="bi bi-chat-dots"></i> 开始聊天
                        </button>
                    </div>
                </div>

                <!-- 会话信息 -->
                <div class="card mb-3" id="session-info-card" style="display: none;">
                    <div class="card-header">
                        <h6 class="mb-0">当前会话</h6>
                    </div>
                    <div class="card-body">
                        <div id="session-info-content">
                            <!-- 会话信息将在这里显示 -->
                        </div>
                        <button class="btn btn-outline-danger btn-sm w-100 mt-2" onclick="clearSession()">
                            <i class="bi bi-trash"></i> 清空会话
                        </button>
                    </div>
                </div>

                <!-- 会话列表 -->
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h6 class="mb-0">会话列表</h6>
                        <button class="btn btn-outline-primary btn-sm" onclick="refreshSessionList()">
                            <i class="bi bi-arrow-clockwise"></i>
                        </button>
                    </div>
                    <div class="card-body">
                        <div id="session-list">
                            <!-- 会话列表将在这里显示 -->
                        </div>
                    </div>
                </div>
            </div>

            <!-- 主聊天区域 -->
            <div class="col-md-9">
                <div class="card h-100">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-chat-square-dots"></i>
                            Data Scope 功能测试
                        </h5>
                    </div>
                    <div class="card-body d-flex flex-column">
                        <!-- 聊天消息区域 -->
                        <div class="flex-grow-1 overflow-auto mb-3" id="chat-messages" style="min-height: 400px;">
                            <div class="text-center text-muted">
                                <i class="bi bi-chat-square-dots" style="font-size: 3rem; opacity: 0.3;"></i>
                                <p class="mt-3">请配置会话参数并点击"开始聊天"</p>
                            </div>
                        </div>

                        <!-- 聊天输入区域 -->
                        <div class="border-top pt-3">
                            <form id="chat-form" onsubmit="sendMessage(event)">
                                <div class="input-group">
                                    <input type="text" class="form-control" id="chat-input" 
                                           placeholder="输入您的问题..." disabled>
                                    <button class="btn btn-primary" type="submit" disabled>
                                        <i class="bi bi-send"></i>
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 引入必要的脚本 -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/marked@5.1.1/marked.min.js"></script>
    
    <!-- 应用状态 -->
    <script>
        const AppState = {
            apiBaseUrl: 'http://localhost:8000',
            systemStatus: 'online',
            currentSession: null
        };
    </script>

    <!-- 引入模块 -->
    <script src="js/indexedDBHelper.js"></script>
    <script src="js/sessionManager.js"></script>
    <script src="js/api.js"></script>

    <script>
        // 页面初始化
        document.addEventListener('DOMContentLoaded', async function() {
            try {
                // 初始化IndexedDB和会话管理器
                await window.sessionManager.init();
                console.log('测试页面初始化完成');
                
                // 加载会话列表
                await refreshSessionList();
                
                // 清理过期会话
                const deletedCount = await window.sessionManager.cleanupExpiredSessions(30);
                if (deletedCount > 0) {
                    console.log(`清理了 ${deletedCount} 个过期会话`);
                }
            } catch (error) {
                console.error('初始化失败:', error);
                alert('初始化失败: ' + error.message);
            }
        });

        // 切换scope字段显示
        function toggleScopeFields() {
            const dataScope = document.getElementById('data-scope').value;
            const courseFields = document.getElementById('course-fields');
            const courseMaterialFields = document.getElementById('course-material-fields');

            if (dataScope === 'course') {
                courseFields.style.display = 'block';
                courseMaterialFields.style.display = 'none';
            } else {
                courseFields.style.display = 'none';
                courseMaterialFields.style.display = 'block';
            }
        }

        // 开始聊天
        async function startChat() {
            try {
                const dataScope = document.getElementById('data-scope').value;
                const mode = document.getElementById('chat-mode').value;

                const sessionConfig = {
                    sessionType: 'course_chat',
                    dataScope: dataScope,
                    mode: mode
                };

                if (dataScope === 'course') {
                    sessionConfig.courseId = document.getElementById('course-id').value;
                    sessionConfig.courseName = document.getElementById('course-name').value;
                } else {
                    sessionConfig.courseMaterialId = document.getElementById('course-material-id').value;
                    sessionConfig.courseMaterialName = document.getElementById('course-material-name').value;
                }

                // 查找或创建会话
                const session = await window.sessionManager.findOrCreateSession(sessionConfig);
                
                // 更新UI
                updateSessionInfo(session);
                loadChatHistory(session);
                enableChatInput();
                
                console.log('会话已启动:', session);
            } catch (error) {
                console.error('启动聊天失败:', error);
                alert('启动聊天失败: ' + error.message);
            }
        }

        // 更新会话信息显示
        function updateSessionInfo(session) {
            const card = document.getElementById('session-info-card');
            const content = document.getElementById('session-info-content');
            
            content.innerHTML = `
                <div class="mb-2">
                    <strong>会话ID:</strong><br>
                    <code class="small">${session.session_id}</code>
                </div>
                <div class="mb-2">
                    <strong>标题:</strong> ${session.title}
                </div>
                <div class="mb-2">
                    <strong>类型:</strong> ${session.session_type}
                </div>
                <div class="mb-2">
                    <strong>数据范围:</strong> ${session.data_scope}
                </div>
                <div class="mb-2">
                    <strong>模式:</strong> ${session.mode}
                </div>
                <div class="mb-2">
                    <strong>消息数:</strong> ${session.frontend_session_chat_history.length}
                </div>
            `;
            
            card.style.display = 'block';
        }

        // 加载聊天历史
        function loadChatHistory(session) {
            const chatContainer = document.getElementById('chat-messages');
            chatContainer.innerHTML = '';

            if (session.frontend_session_chat_history.length === 0) {
                chatContainer.innerHTML = `
                    <div class="text-center text-muted">
                        <i class="bi bi-chat-square-dots" style="font-size: 3rem; opacity: 0.3;"></i>
                        <p class="mt-3">开始您的对话吧！</p>
                    </div>
                `;
                return;
            }

            session.frontend_session_chat_history.forEach(message => {
                addChatMessageToUI(message.role, message.content, false);
            });
        }

        // 启用聊天输入
        function enableChatInput() {
            document.getElementById('chat-input').disabled = false;
            document.querySelector('#chat-form button').disabled = false;
        }

        // 发送消息
        async function sendMessage(event) {
            event.preventDefault();
            
            const input = document.getElementById('chat-input');
            const question = input.value.trim();
            
            if (!question) return;
            
            input.value = '';
            
            // 添加用户消息到UI
            addChatMessageToUI('user', question);
            
            // 添加到会话历史
            await window.sessionManager.addChatMessage('user', question);
            
            // 显示加载状态
            const loadingId = addChatMessageToUI('assistant', '正在思考中...', true);
            
            try {
                // 获取会话RAG参数
                const ragParams = window.sessionManager.getSessionRAGParams();
                
                // 构建查询请求
                const queryData = {
                    question: question,
                    mode: ragParams.mode,
                    session_id: ragParams.session_id,
                    data_scope: ragParams.data_scope,
                    chatSummaryBuffer: ragParams.chatSummaryBuffer
                };

                // 添加相应的ID和名称参数
                if (ragParams.course_id) {
                    queryData.course_id = ragParams.course_id;
                    queryData.course_name = ragParams.course_name;
                }
                if (ragParams.course_material_id) {
                    queryData.course_material_id = ragParams.course_material_id;
                    queryData.course_material_name = ragParams.course_material_name;
                }

                // 调用RAG API
                const response = await RAGAPI.query(queryData);
                
                // 移除加载消息
                removeChatMessage(loadingId);
                
                // 添加助手回复
                addChatMessageToUI('assistant', response.answer);
                
                // 添加到会话历史
                await window.sessionManager.addChatMessage('assistant', response.answer);
                
                // 更新ChatSummaryBuffer
                if (response.chatSummaryBuffer) {
                    await window.sessionManager.updateChatSummaryBuffer(response.chatSummaryBuffer);
                }
                
                // 更新会话信息
                const currentSession = window.sessionManager.getCurrentSession();
                updateSessionInfo(currentSession);
                
            } catch (error) {
                // 移除加载消息
                removeChatMessage(loadingId);
                
                // 显示错误消息
                addChatMessageToUI('assistant', '抱歉，处理您的问题时出现了错误: ' + error.message);
                console.error('查询失败:', error);
            }
        }

        // 添加聊天消息到UI
        function addChatMessageToUI(role, content, isLoading = false) {
            const chatContainer = document.getElementById('chat-messages');
            const messageId = 'msg-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9);

            // 如果是第一条消息，清空欢迎信息
            const welcomeMsg = chatContainer.querySelector('.text-center.text-muted');
            if (welcomeMsg) {
                welcomeMsg.remove();
            }

            const messageDiv = document.createElement('div');
            messageDiv.id = messageId;
            messageDiv.className = `chat-message ${role} ${isLoading ? 'loading' : ''}`;

            if (role === 'assistant') {
                messageDiv.innerHTML = marked.parse(content);
            } else {
                messageDiv.textContent = content;
            }

            chatContainer.appendChild(messageDiv);
            chatContainer.scrollTop = chatContainer.scrollHeight;

            return messageId;
        }

        // 移除聊天消息
        function removeChatMessage(messageId) {
            const messageElement = document.getElementById(messageId);
            if (messageElement) {
                messageElement.remove();
            }
        }

        // 清空会话
        async function clearSession() {
            if (!window.sessionManager.getCurrentSession()) {
                alert('没有活跃的会话');
                return;
            }

            if (!confirm('确定要清空当前会话的聊天记录吗？')) {
                return;
            }

            try {
                await window.sessionManager.clearCurrentSessionHistory();
                
                // 重新加载聊天历史
                const currentSession = window.sessionManager.getCurrentSession();
                loadChatHistory(currentSession);
                updateSessionInfo(currentSession);
                
                console.log('会话已清空');
            } catch (error) {
                console.error('清空会话失败:', error);
                alert('清空会话失败: ' + error.message);
            }
        }

        // 刷新会话列表
        async function refreshSessionList() {
            try {
                const sessions = await window.sessionManager.getSessionList();
                const listContainer = document.getElementById('session-list');
                
                if (sessions.length === 0) {
                    listContainer.innerHTML = '<p class="text-muted small">暂无会话</p>';
                    return;
                }

                listContainer.innerHTML = sessions.map(session => `
                    <div class="border rounded p-2 mb-2 session-item" onclick="loadSession('${session.session_id}')">
                        <div class="fw-bold small">${session.title}</div>
                        <div class="text-muted small">
                            ${session.data_scope} | ${session.mode}
                        </div>
                        <div class="text-muted small">
                            ${new Date(session.updated_at).toLocaleString('zh-CN')}
                        </div>
                    </div>
                `).join('');
            } catch (error) {
                console.error('刷新会话列表失败:', error);
            }
        }

        // 加载指定会话
        async function loadSession(sessionId) {
            try {
                const session = await window.sessionManager.loadSession(sessionId);
                
                // 更新UI
                updateSessionInfo(session);
                loadChatHistory(session);
                enableChatInput();
                
                console.log('会话已加载:', session);
            } catch (error) {
                console.error('加载会话失败:', error);
                alert('加载会话失败: ' + error.message);
            }
        }
    </script>
</body>
</html>
