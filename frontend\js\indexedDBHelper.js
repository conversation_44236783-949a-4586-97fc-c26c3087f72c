/**
 * IndexedDB 操作助手类
 * 用于管理聊天会话的持久化存储
 */
class IndexedDBHelper {
    constructor() {
        this.dbName = 'ChatSessionDB';
        this.version = 1;
        this.storeName = 'sessions';
        this.db = null;
    }

    /**
     * 初始化数据库
     */
    async init() {
        return new Promise((resolve, reject) => {
            const request = indexedDB.open(this.dbName, this.version);

            request.onerror = () => {
                console.error('IndexedDB 打开失败:', request.error);
                reject(request.error);
            };

            request.onsuccess = () => {
                this.db = request.result;
                console.log('IndexedDB 初始化成功');
                resolve(this.db);
            };

            request.onupgradeneeded = (event) => {
                const db = event.target.result;
                
                // 删除旧的对象存储（如果存在）
                if (db.objectStoreNames.contains(this.storeName)) {
                    db.deleteObjectStore(this.storeName);
                }

                // 创建对象存储
                const store = db.createObjectStore(this.storeName, { 
                    keyPath: 'session_id' 
                });

                // 创建索引
                store.createIndex('updated_at', 'updated_at', { unique: false });
                store.createIndex('session_type', 'session_type', { unique: false });
                store.createIndex('data_scope', 'data_scope', { unique: false });
                store.createIndex('course_id', 'course_id', { unique: false });
                store.createIndex('course_material_id', 'course_material_id', { unique: false });
                store.createIndex('title', 'title', { unique: false });

                console.log('IndexedDB 对象存储和索引创建完成');
            };
        });
    }

    /**
     * 创建新会话
     */
    async createSession(sessionData) {
        if (!this.db) {
            throw new Error('数据库未初始化');
        }

        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction([this.storeName], 'readwrite');
            const store = transaction.objectStore(this.storeName);

            // 设置时间戳
            const now = Date.now();
            sessionData.created_at = now;
            sessionData.updated_at = now;

            const request = store.add(sessionData);

            request.onsuccess = () => {
                console.log('会话创建成功:', sessionData.session_id);
                resolve(sessionData);
            };

            request.onerror = () => {
                console.error('会话创建失败:', request.error);
                reject(request.error);
            };
        });
    }

    /**
     * 获取指定会话
     */
    async getSession(sessionId) {
        if (!this.db) {
            throw new Error('数据库未初始化');
        }

        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction([this.storeName], 'readonly');
            const store = transaction.objectStore(this.storeName);
            const request = store.get(sessionId);

            request.onsuccess = () => {
                resolve(request.result);
            };

            request.onerror = () => {
                console.error('获取会话失败:', request.error);
                reject(request.error);
            };
        });
    }

    /**
     * 根据数据范围和对应ID查找现有会话
     */
    async findSessionByDataScope(dataScope, scopeId) {
        if (!this.db) {
            throw new Error('数据库未初始化');
        }

        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction([this.storeName], 'readonly');
            const store = transaction.objectStore(this.storeName);
            const request = store.getAll();

            request.onsuccess = () => {
                const sessions = request.result;
                
                // 根据data_scope和对应ID查找会话
                const foundSession = sessions.find(session => {
                    if (session.data_scope !== dataScope) {
                        return false;
                    }

                    if (dataScope === 'course') {
                        return session.course_id === scopeId;
                    } else if (dataScope === 'course_material') {
                        return session.course_material_id === scopeId;
                    }

                    return false;
                });

                resolve(foundSession || null);
            };

            request.onerror = () => {
                console.error('查找会话失败:', request.error);
                reject(request.error);
            };
        });
    }

    /**
     * 更新会话数据
     */
    async updateSession(sessionId, updates) {
        if (!this.db) {
            throw new Error('数据库未初始化');
        }

        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction([this.storeName], 'readwrite');
            const store = transaction.objectStore(this.storeName);
            
            // 先获取现有会话
            const getRequest = store.get(sessionId);
            
            getRequest.onsuccess = () => {
                const existingSession = getRequest.result;
                if (!existingSession) {
                    reject(new Error('会话不存在'));
                    return;
                }

                // 合并更新数据
                const updatedSession = {
                    ...existingSession,
                    ...updates,
                    updated_at: Date.now()
                };

                const putRequest = store.put(updatedSession);
                
                putRequest.onsuccess = () => {
                    console.log('会话更新成功:', sessionId);
                    resolve(updatedSession);
                };

                putRequest.onerror = () => {
                    console.error('会话更新失败:', putRequest.error);
                    reject(putRequest.error);
                };
            };

            getRequest.onerror = () => {
                console.error('获取会话失败:', getRequest.error);
                reject(getRequest.error);
            };
        });
    }

    /**
     * 删除会话
     */
    async deleteSession(sessionId) {
        if (!this.db) {
            throw new Error('数据库未初始化');
        }

        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction([this.storeName], 'readwrite');
            const store = transaction.objectStore(this.storeName);
            const request = store.delete(sessionId);

            request.onsuccess = () => {
                console.log('会话删除成功:', sessionId);
                resolve(true);
            };

            request.onerror = () => {
                console.error('会话删除失败:', request.error);
                reject(request.error);
            };
        });
    }

    /**
     * 清空指定会话的聊天历史和ChatSummaryBuffer
     */
    async clearSessionHistory(sessionId) {
        const updates = {
            frontend_session_chat_history: [],
            chatSummaryBuffer: null
        };
        return this.updateSession(sessionId, updates);
    }

    /**
     * 获取会话列表（按updated_at倒序排序）
     */
    async listSessions(options = {}) {
        if (!this.db) {
            throw new Error('数据库未初始化');
        }

        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction([this.storeName], 'readonly');
            const store = transaction.objectStore(this.storeName);
            const index = store.index('updated_at');
            const request = index.getAll();

            request.onsuccess = () => {
                let sessions = request.result;
                
                // 按updated_at倒序排序
                sessions.sort((a, b) => b.updated_at - a.updated_at);

                // 应用过滤条件
                if (options.session_type) {
                    sessions = sessions.filter(s => s.session_type === options.session_type);
                }
                if (options.data_scope) {
                    sessions = sessions.filter(s => s.data_scope === options.data_scope);
                }

                resolve(sessions);
            };

            request.onerror = () => {
                console.error('获取会话列表失败:', request.error);
                reject(request.error);
            };
        });
    }

    /**
     * 清理过期会话（超过指定天数未更新的会话）
     */
    async cleanupExpiredSessions(days = 30) {
        if (!this.db) {
            throw new Error('数据库未初始化');
        }

        const cutoffTime = Date.now() - (days * 24 * 60 * 60 * 1000);
        
        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction([this.storeName], 'readwrite');
            const store = transaction.objectStore(this.storeName);
            const index = store.index('updated_at');
            const request = index.openCursor();

            let deletedCount = 0;

            request.onsuccess = (event) => {
                const cursor = event.target.result;
                if (cursor) {
                    if (cursor.value.updated_at < cutoffTime) {
                        cursor.delete();
                        deletedCount++;
                    }
                    cursor.continue();
                } else {
                    console.log(`清理完成，删除了 ${deletedCount} 个过期会话`);
                    resolve(deletedCount);
                }
            };

            request.onerror = () => {
                console.error('清理过期会话失败:', request.error);
                reject(request.error);
            };
        });
    }

    /**
     * 根据会话标题关键词搜索会话
     */
    async searchSessionsByTitle(titleKeyword) {
        if (!this.db) {
            throw new Error('数据库未初始化');
        }

        const sessions = await this.listSessions();
        return sessions.filter(session => 
            session.title && session.title.toLowerCase().includes(titleKeyword.toLowerCase())
        );
    }
}

// 创建全局实例
window.indexedDBHelper = new IndexedDBHelper();
